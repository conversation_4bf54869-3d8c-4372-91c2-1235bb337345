#!/usr/bin/env node

/**
 * Database Setup Script
 *
 * This script sets up the PostgreSQL database with Prisma
 * Handles both development and production environments
 *
 * Features:
 * - Environment detection (dev vs production)
 * - Automatic migration handling
 * - Database connection verification
 * - Redis connection testing
 * - Comprehensive error handling
 *
 * SECURITY: This script should ONLY be run server-side
 */

import { execSync } from 'child_process';
import { existsSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { prisma } from '../libs/prisma.js';
import { cache } from '../libs/redis.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n🔧 [${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function runCommand(command, description, options = {}) {
  try {
    log(`   Running: ${command}`, 'blue');
    const result = execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options
    });
    logSuccess(`${description} completed`);
    return result;
  } catch (error) {
    if (options.allowFailure) {
      logWarning(`${description} failed (continuing): ${error.message}`);
      return null;
    } else {
      logError(`${description} failed: ${error.message}`);
      throw error;
    }
  }
}

function checkEnvironment() {
  logStep('1', 'Checking environment configuration');

  if (!process.env.POSTGRE_DB) {
    logError('POSTGRE_DB environment variable is not set');
    throw new Error('Database URL not configured');
  }

  logSuccess('Database URL is configured');

  const isProduction = process.env.NODE_ENV === 'production';
  log(`   Environment: ${isProduction ? 'Production' : 'Development'}`, 'blue');
}

function generatePrismaClient() {
  logStep('2', 'Generating Prisma client');

  runCommand('npx prisma generate', 'Prisma client generation');
}

function handleMigrations() {
  logStep('3', 'Handling database migrations');

  const isProduction = process.env.NODE_ENV === 'production';
  const migrationsDir = join(__dirname, '../prisma/migrations');

  if (isProduction) {
    log('   Production environment - deploying migrations', 'blue');

    if (existsSync(migrationsDir)) {
      runCommand('npx prisma migrate deploy', 'Migration deployment');
    } else {
      log('   No migrations found - pushing schema directly', 'blue');
      runCommand('npx prisma db push', 'Schema push');
    }
  } else {
    log('   Development environment - handling schema changes', 'blue');

    const hasExistingMigrations = existsSync(migrationsDir) &&
                                  readdirSync(migrationsDir).length > 0;

    if (hasExistingMigrations) {
      log('   Applying existing migrations...', 'blue');
      runCommand('npx prisma migrate deploy', 'Migration deployment');
    } else {
      log('   Pushing schema changes...', 'blue');
      runCommand('npx prisma db push', 'Schema push');
    }
  }
}

async function testConnections() {
  logStep('4', 'Testing connections');

  // Test database connection
  try {
    log('   Testing database connection...', 'blue');
    await prisma.$queryRaw`SELECT 1 as test`;
    logSuccess('Database connection successful');
  } catch (error) {
    logError(`Database connection failed: ${error.message}`);
    throw error;
  }

  // Test Redis connection
  try {
    log('   Testing Redis connection...', 'blue');
    const redisHealth = await cache.ping();
    if (redisHealth) {
      logSuccess('Redis connection successful');
    } else {
      logWarning('Redis connection failed - caching will be disabled');
    }
  } catch (error) {
    logWarning(`Redis connection failed: ${error.message} - caching will be disabled`);
  }
}

function printSetupSummary() {
  logStep('5', 'Setup Summary');

  log('\n🎉 Database setup completed successfully!', 'green');
  log('\n📋 What was done:', 'bright');
  log('   ✅ Environment configuration verified');
  log('   ✅ Prisma client generated');
  log('   ✅ Database migrations handled');
  log('   ✅ Database connection tested');
  log('   ✅ Redis connection tested');

  log('\n🚀 Your database is ready!', 'bright');

  log('\n📚 Next steps:', 'yellow');
  log('   • npm run dev          - Start development server');
  log('   • npm run db:studio    - Open Prisma Studio');
  log('   • npm run db:migrate   - Create new migration (dev)');
  log('   • npm run security:test - Run security tests');
}

async function setupDatabase() {
  log('\n🚀 Starting comprehensive database setup...', 'bright');

  try {
    checkEnvironment();
    generatePrismaClient();
    handleMigrations();
    await testConnections();
    printSetupSummary();

  } catch (error) {
    logError(`Database setup failed: ${error.message}`);
    log('\n🔧 Troubleshooting tips:', 'yellow');
    log('   • Check that POSTGRE_DB environment variable is set correctly');
    log('   • Verify database server is running and accessible');
    log('   • Ensure database user has proper permissions');
    log('   • Check that the database exists');
    log('   • For Redis issues, verify REDIS_URL or Redis configuration');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    try {
      await cache.disconnect();
    } catch (error) {
      // Redis disconnect might fail if connection was never established
      logWarning('Redis disconnect failed (this is normal if Redis was not connected)');
    }
  }
}

// Run the setup
setupDatabase();
