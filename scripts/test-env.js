#!/usr/bin/env node

/**
 * Test script to verify environment variables are loaded correctly
 */

// Load environment variables from .env file
require('dotenv').config();

console.log('🔍 Testing environment variable loading...\n');

const requiredVars = [
  'NEXTAUTH_SECRET',
  'POSTGRE_DB'
];

const optionalVars = [
  'REDIS_PASSWORD',
  'SMTP_PASSWORD',
  'ADMIN_API_KEY',
  'GOOGLE_ID',
  'GOOGLE_SECRET'
];

console.log('📋 Required Variables:');
for (const varName of requiredVars) {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
  }
}

console.log('\n📋 Optional Variables:');
for (const varName of optionalVars) {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`⚠️  ${varName}: NOT SET`);
  }
}

console.log('\n🔍 Environment Info:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
console.log(`PWD: ${process.env.PWD || 'not set'}`);

console.log('\n✅ Environment test completed!');
