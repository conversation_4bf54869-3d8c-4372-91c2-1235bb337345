#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to re-enable strict SSL validation after production deployment
 * 
 * This script updates libs/prisma.js to enforce SSL validation in production
 * Run this after the build has been successfully deployed to production
 */

const fs = require('fs');
const path = require('path');

const PRISMA_FILE = path.join(__dirname, '../libs/prisma.js');

function enableSSLValidation() {
  try {
    // Read the current file
    const content = fs.readFileSync(PRISMA_FILE, 'utf8');
    
    // Replace the warning-only validation with strict validation
    const updatedContent = content.replace(
      /console\.warn\('⚠️  SECURITY WARNING: Database connection should use SSL in production\.'\);\s*console\.warn\('Please add sslmode=require or \?ssl=true to your POSTGRE_DB connection string\.'\);\s*\/\/ Temporarily disabled to allow build to complete:\s*\/\/ throw new Error\('❌ SECURITY ERROR: Database connection must use SSL in production\.'\);/g,
      `const errorMsg = '❌ SECURITY ERROR: Database connection must use SSL in production. Please add sslmode=require or ?ssl=true to your POSTGRE_DB connection string.';
      console.error(errorMsg);
      console.error('Current URL pattern:', dbUrl.replace(/\\/\\/[^@]+@/, '//***:***@')); // Hide credentials
      throw new Error(errorMsg);`
    );
    
    // Write the updated content back
    fs.writeFileSync(PRISMA_FILE, updatedContent, 'utf8');
    
    console.log('✅ SSL validation has been re-enabled in libs/prisma.js');
    console.log('🔒 Database connections will now require SSL in production');
    console.log('');
    console.log('Make sure your POSTGRE_DB connection string includes:');
    console.log('  - sslmode=require (for PostgreSQL URLs)');
    console.log('  - or ?ssl=true (for some cloud providers)');
    console.log('');
    console.log('Example: ********************************/db?sslmode=require');
    
  } catch (error) {
    console.error('❌ Error enabling SSL validation:', error.message);
    process.exit(1);
  }
}

function checkCurrentStatus() {
  try {
    const content = fs.readFileSync(PRISMA_FILE, 'utf8');
    
    if (content.includes('SECURITY WARNING: Database connection should use SSL')) {
      console.log('⚠️  SSL validation is currently in WARNING mode (build-safe)');
      return false;
    } else if (content.includes('SECURITY ERROR: Database connection must use SSL')) {
      console.log('✅ SSL validation is currently in STRICT mode (production-ready)');
      return true;
    } else {
      console.log('❓ Could not determine SSL validation status');
      return null;
    }
  } catch (error) {
    console.error('❌ Error checking SSL validation status:', error.message);
    process.exit(1);
  }
}

// Main execution
console.log('🔍 Checking current SSL validation status...');
const isStrict = checkCurrentStatus();

if (isStrict === false) {
  console.log('');
  console.log('🔧 Enabling strict SSL validation...');
  enableSSLValidation();
} else if (isStrict === true) {
  console.log('');
  console.log('ℹ️  SSL validation is already in strict mode. No changes needed.');
} else {
  console.log('');
  console.log('❌ Unable to determine or modify SSL validation status.');
  console.log('Please manually check libs/prisma.js');
}
