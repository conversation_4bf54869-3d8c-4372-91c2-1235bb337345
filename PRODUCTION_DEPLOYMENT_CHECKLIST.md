# Production Deployment Checklist

This checklist ensures all security fixes are properly deployed and configured in production.

## 🚀 Pre-Deployment Steps

### 1. Environment Variables Setup
Ensure all required environment variables are set with strong values:

```bash
# Authentication (64+ characters recommended)
NEXTAUTH_SECRET="[strong-random-string-64-chars]"

# Admin Access (64+ characters recommended)  
ADMIN_API_KEY="[strong-random-string-64-chars]"

# Database and Services (32+ characters minimum)
REDIS_PASSWORD="[strong-random-string-32-chars]"
SMTP_PASSWORD="[strong-random-string-32-chars]"

# Database with SSL (IMPORTANT!)
POSTGRE_DB="********************************/db?sslmode=require"
# OR for some cloud providers:
POSTGRE_DB="********************************/db?ssl=true"

# SMTP Configuration
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-user"
SMTP_SECURE="false"
SMTP_FROM="<EMAIL>"

# Google OAuth (if enabled)
GOOGLE_ID="your-google-oauth-client-id"
GOOGLE_SECRET="your-google-oauth-client-secret"
```

### 2. Security Configuration Verification
- [ ] All passwords are 32+ characters and don't contain weak patterns
- [ ] Database URL includes SSL configuration (`sslmode=require` or `?ssl=true`)
- [ ] Admin API key is 64+ characters and unique
- [ ] NEXTAUTH_SECRET is 64+ characters and unique

### 3. Build Process
```bash
# Build the application
npm run build

# If build fails with SSL validation error, it's expected
# The fixes in this session handle this gracefully
```

## 🔧 Deployment Steps

### 1. Deploy Application
Deploy the built application to your production server using your preferred method.

### 2. Verify Application Startup
Check that the application starts without errors:
```bash
npm start
```

Look for these success messages:
- ✅ Database connected successfully
- ✅ Redis connected successfully  
- ✅ All services initialized successfully

### 3. Test Authentication
- [ ] Visit the login page
- [ ] Test Google OAuth login (if enabled)
- [ ] Test email magic link login (if enabled)
- [ ] Verify users can access dashboard after login

### 4. Test API Security
- [ ] Verify `/api/users` endpoint requires authentication
- [ ] Test admin API endpoints require valid admin key
- [ ] Verify rate limiting works on `/api/lead`
- [ ] Check security headers are present on API responses

## 🔒 Post-Deployment Security Steps

### 1. Re-enable Strict SSL Validation
After successful deployment, re-enable strict SSL validation:

```bash
# Run the SSL validation enabler script
node scripts/enable-ssl-validation.js
```

This will:
- Convert SSL warnings back to strict errors
- Ensure database connections require SSL in production
- Provide confirmation that SSL is properly configured

### 2. Admin Configuration
Update admin middleware with production settings:

```javascript
// In app/api/admin/middleware.js
const adminApiKeys = [
  {
    key: process.env.ADMIN_API_KEY,
    allowedIps: ['YOUR_PRODUCTION_IP', 'YOUR_OFFICE_IP'], // Remove localhost IPs
    expiresAt: null, // Or set expiration date
  },
];
```

### 3. Security Monitoring Setup
- [ ] Monitor admin access logs for unauthorized attempts
- [ ] Set up alerts for rate limit violations
- [ ] Monitor application logs for security warnings
- [ ] Verify SSL certificates are valid and not expiring soon

## ✅ Verification Tests

### 1. Run Security Test Suite
```bash
npm test test/security-audit.test.js
```

### 2. Manual Security Checks
- [ ] Try accessing `/api/users` without authentication (should return 401)
- [ ] Try admin endpoints without API key (should return 401)
- [ ] Verify CORS headers are set correctly
- [ ] Check that sensitive data is not exposed in error messages
- [ ] Test rate limiting by making multiple rapid requests

### 3. Database Security Verification
- [ ] Confirm database connection uses SSL
- [ ] Verify database credentials are not logged
- [ ] Test that database queries are properly parameterized

## 🚨 Security Incident Response

If any security issues are discovered:

1. **Immediate Actions**:
   - Document the issue
   - Assess impact and affected users
   - Implement temporary mitigation if needed

2. **Investigation**:
   - Check logs for unauthorized access
   - Verify all environment variables are secure
   - Review recent code changes

3. **Resolution**:
   - Apply security fixes
   - Update passwords/keys if compromised
   - Re-run security test suite
   - Document lessons learned

## 📋 Regular Maintenance

### Weekly
- [ ] Review admin access logs
- [ ] Check for dependency updates with security patches
- [ ] Verify SSL certificates are not expiring soon

### Monthly  
- [ ] Run full security test suite
- [ ] Review and rotate API keys if needed
- [ ] Update dependencies with security patches
- [ ] Review access logs for unusual patterns

### Quarterly
- [ ] Conduct comprehensive security audit
- [ ] Review and update security policies
- [ ] Test disaster recovery procedures
- [ ] Update security documentation

## 📞 Emergency Contacts

Document emergency contacts for security incidents:
- System Administrator: [contact info]
- Database Administrator: [contact info]  
- Security Team: [contact info]
- Hosting Provider Support: [contact info]

---

## 📚 Related Documentation

- [SECURITY_AUDIT_REPORT.md](SECURITY_AUDIT_REPORT.md) - Latest security audit findings
- [SECURITY_UPDATES.md](SECURITY_UPDATES.md) - Security update history
- [docs/SECURITY_GUIDE.md](docs/SECURITY_GUIDE.md) - Comprehensive security guide
- [test/security-audit.test.js](test/security-audit.test.js) - Security test suite

---

**Remember**: Security is an ongoing process. This checklist should be updated as new security measures are implemented and new threats are discovered.
