/* eslint-env jest */
// Tests for the automated deployment system

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

describe('Deployment Automation Tests', () => {
  describe('Package.json Configuration', () => {
    it('should have postbuild script configured', () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      expect(packageJson.scripts.postbuild).toBe('node scripts/postbuild.js');
    });

    it('should have all required database scripts', () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      const requiredScripts = [
        'db:setup',
        'db:generate', 
        'db:migrate',
        'db:migrate:deploy',
        'db:migrate:reset',
        'db:studio'
      ];

      for (const script of requiredScripts) {
        expect(packageJson.scripts[script]).toBeDefined();
      }
    });

    it('should have security scripts configured', () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      expect(packageJson.scripts['security:test']).toBeDefined();
      expect(packageJson.scripts['security:enable-ssl']).toBeDefined();
    });

    it('should have Prisma seed configuration', () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      expect(packageJson.prisma).toBeDefined();
      expect(packageJson.prisma.seed).toBe('node prisma/seed.js');
    });
  });

  describe('Script Files Existence', () => {
    it('should have postbuild script file', () => {
      expect(fs.existsSync('scripts/postbuild.js')).toBe(true);
    });

    it('should have setup-database script file', () => {
      expect(fs.existsSync('scripts/setup-database.js')).toBe(true);
    });

    it('should have seed script file', () => {
      expect(fs.existsSync('prisma/seed.js')).toBe(true);
    });

    it('should have enable-ssl-validation script file', () => {
      expect(fs.existsSync('scripts/enable-ssl-validation.js')).toBe(true);
    });

    it('should have automated deployment documentation', () => {
      expect(fs.existsSync('docs/AUTOMATED_DEPLOYMENT.md')).toBe(true);
    });
  });

  describe('Script Permissions', () => {
    it('should have executable permissions on scripts', () => {
      const scripts = [
        'scripts/postbuild.js',
        'scripts/setup-database.js',
        'scripts/enable-ssl-validation.js',
        'prisma/seed.js'
      ];

      for (const script of scripts) {
        const stats = fs.statSync(script);
        const isExecutable = !!(stats.mode & parseInt('111', 8));
        expect(isExecutable).toBe(true);
      }
    });
  });

  describe('Script Content Validation', () => {
    it('should have proper shebang in scripts', () => {
      const scripts = [
        'scripts/postbuild.js',
        'scripts/setup-database.js',
        'scripts/enable-ssl-validation.js',
        'prisma/seed.js'
      ];

      for (const script of scripts) {
        const content = fs.readFileSync(script, 'utf8');
        expect(content.startsWith('#!/usr/bin/env node')).toBe(true);
      }
    });

    it('should have environment detection in postbuild script', () => {
      const content = fs.readFileSync('scripts/postbuild.js', 'utf8');
      
      expect(content).toContain('NODE_ENV');
      expect(content).toContain('production');
      expect(content).toContain('prisma migrate deploy');
    });

    it('should have SSL validation re-enablement in postbuild script', () => {
      const content = fs.readFileSync('scripts/postbuild.js', 'utf8');
      
      expect(content).toContain('enableSSLValidation');
      expect(content).toContain('SSL validation');
    });

    it('should have comprehensive error handling', () => {
      const postbuildContent = fs.readFileSync('scripts/postbuild.js', 'utf8');
      const setupContent = fs.readFileSync('scripts/setup-database.js', 'utf8');
      
      expect(postbuildContent).toContain('try {');
      expect(postbuildContent).toContain('catch');
      expect(setupContent).toContain('try {');
      expect(setupContent).toContain('catch');
    });
  });

  describe('Environment Safety', () => {
    it('should handle missing environment variables gracefully', () => {
      const postbuildContent = fs.readFileSync('scripts/postbuild.js', 'utf8');
      
      expect(postbuildContent).toContain('checkEnvironment');
      expect(postbuildContent).toContain('NEXTAUTH_SECRET');
      expect(postbuildContent).toContain('POSTGRE_DB');
    });

    it('should have production safety checks in seed script', () => {
      const seedContent = fs.readFileSync('prisma/seed.js', 'utf8');
      
      expect(seedContent).toContain('NODE_ENV');
      expect(seedContent).toContain('production');
      expect(seedContent).toContain('Skipping seeding');
    });
  });

  describe('Documentation', () => {
    it('should have comprehensive deployment documentation', () => {
      const docContent = fs.readFileSync('docs/AUTOMATED_DEPLOYMENT.md', 'utf8');
      
      expect(docContent).toContain('Automated Deployment Guide');
      expect(docContent).toContain('postbuild.js');
      expect(docContent).toContain('Prisma migrations');
      expect(docContent).toContain('SSL validation');
      expect(docContent).toContain('set and forget');
    });

    it('should document all available scripts', () => {
      const docContent = fs.readFileSync('docs/AUTOMATED_DEPLOYMENT.md', 'utf8');
      
      expect(docContent).toContain('db:setup');
      expect(docContent).toContain('db:migrate');
      expect(docContent).toContain('security:test');
      expect(docContent).toContain('postbuild');
    });
  });

  describe('Integration Tests', () => {
    it('should be able to run help commands without errors', () => {
      // Test that scripts can at least be executed (they might fail due to missing env vars, but shouldn't have syntax errors)
      
      try {
        // These should not throw syntax errors
        execSync('node -c scripts/postbuild.js', { stdio: 'pipe' });
        execSync('node -c scripts/enable-ssl-validation.js', { stdio: 'pipe' });
        execSync('node -c prisma/seed.js', { stdio: 'pipe' });
      } catch (error) {
        // If it's a syntax error, the test should fail
        if (error.message.includes('SyntaxError')) {
          throw error;
        }
        // Other errors (like missing env vars) are expected and OK
      }
      
      expect(true).toBe(true); // If we get here, no syntax errors
    });
  });
});

// Helper function to check if a script would run successfully
function checkScriptSyntax(scriptPath) {
  try {
    execSync(`node -c ${scriptPath}`, { stdio: 'pipe' });
    return true;
  } catch (error) {
    if (error.message.includes('SyntaxError')) {
      return false;
    }
    return true; // Other errors are OK for syntax check
  }
}

module.exports = { checkScriptSyntax };
