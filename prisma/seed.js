#!/usr/bin/env node

/**
 * Prisma Database Seeder
 *
 * This script seeds the database with initial data
 * Run with: npx prisma db seed
 *
 * SECURITY: This script should ONLY be run server-side
 */

// Load environment variables from .env file
require('dotenv').config();

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');
  
  try {
    // Check if we're in production
    if (process.env.NODE_ENV === 'production') {
      console.log('⚠️  Production environment detected');
      console.log('   Seeding is typically not needed in production');
      console.log('   Skipping seeding to prevent data conflicts');
      return;
    }
    
    console.log('🔍 Checking existing data...');
    
    // Check if users already exist
    const userCount = await prisma.user.count();
    console.log(`   Found ${userCount} existing users`);
    
    if (userCount > 0) {
      console.log('✅ Database already contains data - skipping seeding');
      return;
    }
    
    console.log('📝 Creating initial data...');
    
    // You can add initial data here if needed
    // Example:
    /*
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        hasAccess: true,
        // Add other required fields
      },
    });
    
    console.log(`✅ Created admin user: ${adminUser.email}`);
    */
    
    console.log('✅ Database seeding completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('   • No initial data was needed');
    console.log('   • Database is ready for use');
    console.log('');
    console.log('💡 To add seed data:');
    console.log('   1. Edit prisma/seed.js');
    console.log('   2. Add your initial data creation logic');
    console.log('   3. Run: npx prisma db seed');
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Seeding error:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
