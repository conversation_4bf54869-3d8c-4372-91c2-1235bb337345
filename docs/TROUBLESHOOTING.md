# Troubleshooting Guide

This guide helps resolve common issues with the automated deployment system.

## 🔧 Environment Variable Issues

### Problem: "Required environment variable X is not set"

**Symptoms**:
```
❌ Required environment variable NEXTAUTH_SECRET is not set
❌ Required environment variable POSTGRE_DB is not set
```

**Solutions**:

1. **Test Environment Variable Loading**:
   ```bash
   npm run test:env
   ```
   This will show you which variables are loaded and which are missing.

2. **Check .env File Location**:
   - Ensure `.env` file is in the project root (same directory as `package.json`)
   - Verify the file is named exactly `.env` (not `.env.local` or `.env.production`)

3. **Check .env File Format**:
   ```bash
   # Correct format (no spaces around =)
   NEXTAUTH_SECRET=your-secret-here
   POSTGRE_DB=********************************/db
   
   # Incorrect format
   NEXTAUTH_SECRET = your-secret-here  # ❌ Spaces around =
   NEXTAUTH_SECRET= your-secret-here   # ❌ Space after =
   ```

4. **Verify File Permissions**:
   ```bash
   ls -la .env
   # Should show readable permissions
   ```

5. **Manual Environment Variable Check**:
   ```bash
   # On Linux/Mac
   echo $NEXTAUTH_SECRET
   
   # If using export
   export NEXTAUTH_SECRET="your-secret"
   echo $NEXTAUTH_SECRET
   ```

## 🗄️ Database Connection Issues

### Problem: "Database connection failed"

**Symptoms**:
```
❌ Database connection failed: connect ECONNREFUSED
❌ Database migration deployment failed
```

**Solutions**:

1. **Test Database URL Format**:
   ```bash
   # Correct PostgreSQL URL format
   POSTGRE_DB="postgresql://username:password@host:port/database?sslmode=require"
   
   # Examples:
   # Local development
   POSTGRE_DB="postgresql://postgres:password@localhost:5432/myapp"
   
   # Production with SSL
   POSTGRE_DB="postgresql://user:<EMAIL>:5432/db?sslmode=require"
   ```

2. **Verify Database Server**:
   ```bash
   # Test if database server is reachable
   pg_isready -h your-host -p 5432 -U your-username
   
   # Or use telnet
   telnet your-host 5432
   ```

3. **Check Database Exists**:
   ```bash
   # Connect manually to verify
   psql "postgresql://username:password@host:port/database"
   ```

4. **SSL Configuration**:
   - For production, ensure `?sslmode=require` is in the URL
   - For local development, you might need `?sslmode=disable`

## 🔄 Prisma Migration Issues

### Problem: "Migration deployment failed"

**Symptoms**:
```
❌ Migration deployment failed
❌ Schema push failed
```

**Solutions**:

1. **Check Migration Status**:
   ```bash
   npx prisma migrate status
   ```

2. **Reset Migrations (Development Only)**:
   ```bash
   npm run db:migrate:reset
   # WARNING: This deletes all data!
   ```

3. **Manual Migration**:
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Push schema directly (development)
   npm run db:push
   
   # Deploy migrations (production)
   npm run db:migrate:deploy
   ```

4. **Check Schema File**:
   - Verify `prisma/schema.prisma` exists and is valid
   - Check for syntax errors in the schema

## 🔒 SSL Validation Issues

### Problem: "Database connection must use SSL in production"

**Symptoms**:
```
❌ SECURITY ERROR: Database connection must use SSL in production
```

**Solutions**:

1. **Add SSL to Database URL**:
   ```bash
   # Add sslmode=require
   POSTGRE_DB="********************************/db?sslmode=require"
   
   # Or for some cloud providers
   POSTGRE_DB="********************************/db?ssl=true"
   ```

2. **Temporarily Disable SSL Validation** (not recommended):
   ```bash
   npm run security:enable-ssl
   # This will show current status and allow manual control
   ```

## 🚀 Build and Deployment Issues

### Problem: "Build failed with exit code 1"

**Symptoms**:
```
❌ Post-build setup failed
ELIFECYCLE Command failed with exit code 1
```

**Solutions**:

1. **Check Individual Steps**:
   ```bash
   # Test environment loading
   npm run test:env
   
   # Test database setup
   npm run db:setup
   
   # Test security
   npm run security:test
   ```

2. **Run Build with Verbose Output**:
   ```bash
   npm run build --verbose
   ```

3. **Skip Postbuild Temporarily**:
   ```bash
   # Temporarily rename postbuild script
   # In package.json, change "postbuild" to "postbuild-disabled"
   npm run build
   
   # Then run postbuild manually
   node scripts/postbuild.js
   ```

## 🔍 Redis Connection Issues

### Problem: "Redis connection failed"

**Symptoms**:
```
⚠️ Redis connection failed - caching will be disabled
```

**Solutions**:

1. **Check Redis Configuration**:
   ```bash
   # Verify Redis environment variables
   echo $REDIS_HOST
   echo $REDIS_PORT
   echo $REDIS_PASSWORD
   ```

2. **Test Redis Connection**:
   ```bash
   # If Redis is local
   redis-cli ping
   
   # If Redis is remote
   redis-cli -h your-host -p 6379 -a your-password ping
   ```

3. **Redis is Optional**:
   - The application will work without Redis
   - Caching will be disabled but functionality remains
   - This is a warning, not an error

## 📧 SMTP/Email Issues

### Problem: "SMTP connection failed"

**Symptoms**:
```
⚠️ SMTP connection failed
❌ Email sending failed
```

**Solutions**:

1. **Check SMTP Configuration**:
   ```bash
   # Verify SMTP environment variables
   npm run test:env | grep SMTP
   ```

2. **Test SMTP Settings**:
   ```bash
   # Common SMTP ports
   # 587 - TLS/STARTTLS
   # 465 - SSL
   # 25  - Plain (usually blocked)
   ```

3. **SMTP Provider Examples**:
   ```bash
   # Gmail
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=false
   
   # Outlook
   SMTP_HOST=smtp-mail.outlook.com
   SMTP_PORT=587
   SMTP_SECURE=false
   ```

## 🛠️ General Debugging Steps

### 1. Enable Debug Mode
```bash
# Set debug environment
export DEBUG=*
npm run build
```

### 2. Check Logs
```bash
# Check application logs
npm start 2>&1 | tee app.log

# Check system logs (Linux)
journalctl -u your-app-service
```

### 3. Verify Dependencies
```bash
# Check if all dependencies are installed
npm install

# Check for outdated packages
npm outdated

# Check for security vulnerabilities
npm audit
```

### 4. Clean Build
```bash
# Clean Next.js cache
rm -rf .next

# Clean node_modules
rm -rf node_modules
npm install

# Clean Prisma
npx prisma generate
```

## 📞 Getting Help

If you're still having issues:

1. **Check the logs** for specific error messages
2. **Run the test commands** to isolate the problem
3. **Verify environment variables** are set correctly
4. **Check database connectivity** manually
5. **Review the documentation** for your specific setup

### Useful Commands for Debugging
```bash
# Test everything step by step
npm run test:env           # Test environment variables
npm run db:setup          # Test database setup
npm run security:test     # Test security configuration
node scripts/postbuild.js # Test postbuild script manually
```

---

**Remember**: Most issues are related to environment variables or database connectivity. Start with `npm run test:env` to verify your configuration.
