# Security Guide - StalkAPI Frontend

This document provides comprehensive security guidelines and best practices for the StalkAPI Next.js frontend application.

## 🛡️ Security Overview

The StalkAPI frontend implements bank-level security standards with multiple layers of protection:

- **Authentication & Authorization**: NextAuth.js with JWT sessions and configurable providers
- **Input Validation**: Zod-based validation on all user inputs
- **Rate Limiting**: Redis-based rate limiting on API endpoints
- **Security Headers**: Comprehensive security headers on all responses
- **Data Protection**: Server-side only database access with encrypted connections
- **Admin Security**: Enhanced admin API key validation and monitoring

## 🔐 Authentication Security

### Session Management
- **JWT Strategy**: Secure JWT tokens with 1-hour expiry
- **Strong Secrets**: NEXTAUTH_SECRET must be 32+ characters and not contain weak patterns
- **Session Validation**: Automatic session expiry and token validation

### Provider Security
- **Google OAuth**: Secure OAuth2 flow with proper scope validation
- **Email Magic Links**: Secure email-based authentication with 24-hour expiry
- **Configurable Providers**: Easy enable/disable of authentication methods

### Implementation
```javascript
// Strong secret validation (libs/next-auth.js)
if (!process.env.NEXTAUTH_SECRET || 
    process.env.NEXTAUTH_SECRET.length < 32 || 
    /secret|changeme|default|1234|password/i.test(process.env.NEXTAUTH_SECRET)) {
  throw new Error('NEXTAUTH_SECRET must be strong');
}
```

## 🔒 API Security

### Input Validation
All API endpoints use Zod schemas for input validation:

```javascript
// Example: Lead API validation
const LeadSchema = z.object({
  email: z.string().email().trim().toLowerCase(),
});
```

### Rate Limiting
- **Lead API**: 5 requests per minute per IP
- **Redis-based**: Distributed rate limiting across instances
- **Configurable**: Easy to adjust limits per endpoint

### Security Headers
All API responses include comprehensive security headers:
- `Content-Security-Policy`: Prevents XSS attacks
- `X-Content-Type-Options`: Prevents MIME sniffing
- `X-Frame-Options`: Prevents clickjacking
- `X-XSS-Protection`: Browser XSS protection
- `Referrer-Policy`: Controls referrer information

## 🔑 Admin Security

### API Key Management
- **Strong Keys**: Minimum 32 characters, no weak patterns
- **Environment Validation**: Startup checks for production keys
- **IP Allowlisting**: Optional IP restrictions for admin access
- **Expiration**: Configurable key expiration dates

### Security Logging
All admin access attempts are logged:
```javascript
// Successful access
console.log('✅ Admin API access granted to IP:', clientIp);

// Failed attempts
console.warn('❌ Admin API access attempt with invalid key from IP:', clientIp);
```

### Implementation
```javascript
// Admin key validation (app/api/admin/middleware.js)
if (process.env.ADMIN_API_KEY) {
  const key = process.env.ADMIN_API_KEY;
  if (key.length < 32 || /demo|admin|test|changeme|default|1234|password/i.test(key)) {
    throw new Error('ADMIN_API_KEY must be strong');
  }
}
```

## 🗄️ Database Security

### Connection Security
- **SSL Required**: Production databases must use SSL connections
- **Environment Validation**: Startup checks for SSL configuration
- **Connection Pooling**: Secure connection management with Prisma

### Query Security
- **Prisma ORM**: Type-safe queries prevent SQL injection
- **Parameterized Queries**: All user input is properly parameterized
- **Server-Side Only**: Database clients never exposed to client-side

### Implementation
```javascript
// SSL enforcement (libs/prisma.js)
if (process.env.NODE_ENV === 'production') {
  const dbUrl = process.env.POSTGRE_DB || '';
  if (!/sslmode=require|[?&]ssl=true/i.test(dbUrl)) {
    throw new Error('Database must use SSL in production');
  }
}
```

## 📧 Email Security

### SMTP Security
- **Strong Passwords**: SMTP_PASSWORD must be 16+ characters
- **TLS Encryption**: Secure email transmission
- **Environment Validation**: Production password requirements

### Implementation
```javascript
// SMTP password validation (libs/email.js)
if (process.env.NODE_ENV === 'production') {
  const pwd = process.env.SMTP_PASSWORD;
  if (!pwd || pwd.length < 16 || /smtp|changeme|default|1234|password/i.test(pwd)) {
    throw new Error('SMTP_PASSWORD must be strong in production');
  }
}
```

## 🔄 Redis Security

### Connection Security
- **Password Protection**: Redis password required in production
- **Strong Passwords**: Minimum 16 characters, no weak patterns
- **Network Security**: Restrict Redis network access

### Implementation
```javascript
// Redis password validation (libs/redis.js)
if (process.env.NODE_ENV === 'production') {
  const pwd = process.env.REDIS_PASSWORD;
  if (!pwd || pwd.length < 16 || /redis|changeme|default|1234|password/i.test(pwd)) {
    throw new Error('REDIS_PASSWORD must be strong in production');
  }
}
```

## 🌐 CORS Security

### Environment-Based Configuration
- **Development**: Allow all origins for development flexibility
- **Production**: Restrict to `https://data.stalkapi.com` only
- **Credentials**: Proper handling of credentials in CORS requests

### Implementation
```javascript
// CORS configuration (app/api/middleware.js)
const allowedOrigin = process.env.NODE_ENV === 'production'
  ? 'https://data.stalkapi.com'
  : '*';
```

## 🧪 Security Testing

### Automated Testing
Comprehensive security test suite in `test/security-audit.test.js`:

- **Authentication Tests**: Verify endpoint protection
- **Input Validation Tests**: Test malicious input handling
- **Rate Limiting Tests**: Verify rate limit enforcement
- **Security Headers Tests**: Check header presence
- **Data Exposure Tests**: Prevent sensitive data leaks

### Running Security Tests
```bash
# Run security audit tests
npm test test/security-audit.test.js

# Run all tests including security
npm test
```

## 🚀 Production Security Checklist

### Environment Variables
Ensure all production environment variables are set with strong values:

```bash
# Required strong secrets (64+ characters recommended)
NEXTAUTH_SECRET="[strong-random-string-64-chars]"
ADMIN_API_KEY="[strong-random-string-64-chars]"

# Database and service passwords (32+ characters)
REDIS_PASSWORD="[strong-random-string-32-chars]"
SMTP_PASSWORD="[strong-random-string-32-chars]"

# Database with SSL
POSTGRE_DB="********************************/db?sslmode=require"
```

### Security Configuration
- [ ] All environment variables set with strong values
- [ ] Database SSL enabled and verified
- [ ] Admin IP allowlist configured for production
- [ ] CORS origins restricted to production domain
- [ ] Security headers enabled on all routes
- [ ] Rate limiting configured appropriately
- [ ] Security monitoring and logging enabled

### Monitoring
- [ ] Admin access logs monitored
- [ ] Rate limit violations tracked
- [ ] Failed authentication attempts logged
- [ ] Security test suite runs regularly
- [ ] Dependency vulnerability scanning enabled

## 🔍 Security Monitoring

### Log Monitoring
Monitor these security-related logs:
- Admin API access attempts (successful and failed)
- Rate limit violations
- Authentication failures
- Input validation failures
- Database connection issues

### Alerting
Set up alerts for:
- Multiple failed admin login attempts
- Unusual rate limit violations
- Security test failures
- Dependency vulnerabilities

## 📚 Additional Resources

- [SECURITY_AUDIT_REPORT.md](../SECURITY_AUDIT_REPORT.md) - Latest security audit findings
- [SECURITY_UPDATES.md](../SECURITY_UPDATES.md) - Security update history
- [test/security-audit.test.js](../test/security-audit.test.js) - Security test suite
- [NextAuth.js Security](https://next-auth.js.org/configuration/options#security)
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)

---

**Remember**: Security is an ongoing process. Regularly review and update security measures, run security tests, and stay informed about new vulnerabilities and best practices.
