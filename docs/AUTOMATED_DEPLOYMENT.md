# Automated Deployment Guide

This guide explains the automated deployment system that handles Prisma migrations, SSL validation, and security checks automatically.

## 🚀 Overview

The deployment system is now fully automated with these key features:

- **Automatic Prisma migrations** in production
- **SSL validation re-enablement** after build
- **Environment verification** and security checks
- **Database setup and seeding** (if needed)
- **Comprehensive error handling** and logging

## 📋 Automated Scripts

### 1. Post-Build Script (`scripts/postbuild.js`)

**Runs automatically after**: `npm run build`

**What it does**:
1. ✅ Checks environment variables are properly configured
2. ✅ Generates Prisma client
3. ✅ Deploys database migrations (production) or pushes schema (development)
4. ✅ Re-enables SSL validation for security
5. ✅ Generates sitemap
6. ✅ Runs security audit tests
7. ✅ Provides deployment summary

**Environment Detection**:
- **Production**: Uses `prisma migrate deploy` (safe, non-interactive)
- **Development**: Uses `prisma db push` or existing migrations

### 2. Database Setup Script (`scripts/setup-database.js`)

**Run manually with**: `npm run db:setup`

**What it does**:
1. ✅ Verifies database connection
2. ✅ Generates Prisma client
3. ✅ Handles migrations intelligently based on environment
4. ✅ Tests database and Redis connections
5. ✅ Provides setup summary and next steps

### 3. Database Seeder (`prisma/seed.js`)

**Runs automatically** when needed or manually with `npx prisma db seed`

**What it does**:
- ✅ Checks if initial data is needed
- ✅ Skips seeding in production (safety)
- ✅ Creates initial data only if database is empty
- ✅ Provides template for adding seed data

## 🔧 Package.json Scripts

Your `package.json` now includes these automated scripts:

```json
{
  "scripts": {
    "build": "next build",
    "postbuild": "node scripts/postbuild.js",        // 🤖 Runs automatically after build
    "start": "next start",
    "db:setup": "node scripts/setup-database.js",    // 🔧 Manual database setup
    "db:generate": "prisma generate",                 // 🔧 Generate Prisma client
    "db:migrate": "prisma migrate dev",               // 🔧 Create new migration (dev)
    "db:migrate:deploy": "prisma migrate deploy",     // 🔧 Deploy migrations (prod)
    "db:migrate:reset": "prisma migrate reset",       // 🔧 Reset database (dev only)
    "security:test": "npm test test/security-audit.test.js",  // 🔒 Security tests
    "security:enable-ssl": "node scripts/enable-ssl-validation.js"  // 🔒 Manual SSL enable
  },
  "prisma": {
    "seed": "node prisma/seed.js"                     // 🌱 Automatic seeding
  }
}
```

## 🚀 Deployment Workflow

### For Production Deployment:

1. **Set Environment Variables**:
   ```bash
   export NODE_ENV=production
   export POSTGRE_DB="********************************/db?sslmode=require"
   export NEXTAUTH_SECRET="your-64-char-secret"
   export ADMIN_API_KEY="your-64-char-admin-key"
   # ... other variables
   ```

2. **Test Environment Variables** (optional but recommended):
   ```bash
   npm run test:env    # Verify all environment variables are loaded correctly
   ```

3. **Build and Deploy**:
   ```bash
   npm run build    # This automatically runs postbuild script
   npm start        # Start the application
   ```

3. **That's it!** 🎉 Everything else is automated:
   - Prisma client generation ✅
   - Database migrations ✅
   - SSL validation ✅
   - Security checks ✅
   - Sitemap generation ✅

### For Development Setup:

1. **Initial Setup**:
   ```bash
   npm install
   npm run db:setup    # One-time database setup
   ```

2. **Daily Development**:
   ```bash
   npm run dev         # Start development server
   ```

3. **When Schema Changes**:
   ```bash
   npm run db:migrate  # Create new migration
   # OR
   npm run db:push     # Push schema changes directly
   ```

## 🔍 What Happens During Build

### Step 1: Environment Check
```
🔧 [1] Checking environment configuration
   ✅ NEXTAUTH_SECRET is configured
   ✅ POSTGRE_DB is configured
   ✅ ADMIN_API_KEY is configured
   ⚠️  Optional environment variable REDIS_PASSWORD is not set
```

### Step 2: Prisma Setup
```
🔧 [2] Setting up Prisma database
   Running: npx prisma generate
   ✅ Prisma client generation completed
   Production environment detected - running migrations
   Running: npx prisma migrate deploy
   ✅ Database migration deployment completed
```

### Step 3: SSL Validation
```
🔧 [3] Re-enabling SSL validation
   ✅ SSL validation has been re-enabled
   ✅ Database SSL configuration verified
```

### Step 4: Sitemap Generation
```
🔧 [4] Generating sitemap
   Running: npx next-sitemap
   ✅ Sitemap generation completed
```

### Step 5: Security Checks
```
🔧 [5] Running security checks
   Running: npm test test/security-audit.test.js
   ✅ Security audit tests completed
```

### Step 6: Summary
```
🎉 Post-build setup completed successfully!

📋 What was done:
   ✅ Environment variables verified
   ✅ Prisma client generated
   ✅ Database migrations deployed
   ✅ SSL validation re-enabled
   ✅ Sitemap generated
   ✅ Security checks completed

🚀 Your application is ready for production!
```

## 🛠️ Troubleshooting

### Build Fails with Database Error
```bash
# Check database connection
npm run db:setup

# Verify environment variables
echo $POSTGRE_DB
echo $NEXTAUTH_SECRET
```

### Migrations Fail
```bash
# Reset migrations (development only)
npm run db:migrate:reset

# Or push schema directly
npm run db:push
```

### SSL Validation Issues
```bash
# Manually re-enable SSL validation
npm run security:enable-ssl

# Check database URL format
echo $POSTGRE_DB
# Should include: ?sslmode=require or ?ssl=true
```

### Security Tests Fail
```bash
# Run security tests manually
npm run security:test

# Check security configuration
cat SECURITY_AUDIT_REPORT.md
```

## 🔒 Security Features

The automated deployment includes these security measures:

1. **Environment Validation**: Checks all required variables are set
2. **SSL Enforcement**: Automatically re-enables database SSL validation
3. **Security Testing**: Runs comprehensive security audit
4. **Admin Key Validation**: Ensures strong admin API keys
5. **Error Handling**: Prevents sensitive information disclosure

## 📚 Manual Commands (When Needed)

```bash
# Database Management
npm run db:studio          # Open Prisma Studio
npm run db:generate        # Regenerate Prisma client
npm run db:migrate         # Create new migration
npm run db:push           # Push schema changes
npm run db:migrate:deploy  # Deploy migrations

# Security
npm run security:test      # Run security audit
npm run security:enable-ssl # Re-enable SSL validation

# Environment Testing
npm run test:env          # Test environment variable loading

# Development
npm run dev               # Start development server
npm run build             # Build for production
npm run start             # Start production server
```

## 🎯 Benefits

✅ **Zero Manual Steps**: Everything happens automatically  
✅ **Environment Aware**: Different behavior for dev vs production  
✅ **Error Recovery**: Comprehensive error handling and troubleshooting  
✅ **Security First**: Automatic security validation and testing  
✅ **Beginner Friendly**: No need to remember Prisma commands  
✅ **Production Ready**: Safe, non-interactive migrations  

---

**Remember**: This system is designed to be "set and forget" - just run `npm run build` and everything else is handled automatically!
