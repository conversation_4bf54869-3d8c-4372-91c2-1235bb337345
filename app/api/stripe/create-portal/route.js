import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys } from "@/libs/redis";
import { createCustomerPortal } from "@/libs/stripe";
import { z } from "zod";

// Input validation schema
const PortalSchema = z.object({
  returnUrl: z.string().url("Return URL must be a valid URL")
});

export async function POST(req) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    );
  }

  try {
    const body = await req.json();

    // SECURITY: Validate input with Zod
    const parseResult = PortalSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json(
        { error: "Invalid input", details: parseResult.error.errors },
        { status: 400 }
      );
    }

    const { returnUrl } = parseResult.data;
    const { id } = session.user;

    // Check cache first
    const userCacheKey = CacheKeys.user(id);
    let user = await cache.get(userCacheKey);

    if (!user) {
      user = await prisma.user.findUnique({
        where: { id }
      });

      if (user) {
        // Cache user for 1 hour
        await cache.set(userCacheKey, user, 3600);
      }
    }

    if (!user?.customerId) {
      return NextResponse.json(
        {
          error:
            "You don't have a billing account yet. Make a purchase first.",
        },
        { status: 400 }
      );
    }

    const stripePortalUrl = await createCustomerPortal({
      customerId: user.customerId,
      returnUrl,
    });

    return NextResponse.json({
      url: stripePortalUrl,
    });
  } catch (e) {
    console.error('Stripe portal error:', e.message);
    return NextResponse.json({ error: "Failed to create customer portal" }, { status: 500 });
  }
}
