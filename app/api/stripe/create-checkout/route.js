import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { createCheckout } from "@/libs/stripe";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys } from "@/libs/redis";
import { z } from "zod";

// Input validation schema
const CheckoutSchema = z.object({
  priceId: z.string().min(1, "Price ID is required"),
  mode: z.enum(['payment', 'subscription'], {
    errorMap: () => ({ message: "Mode must be either 'payment' or 'subscription'" })
  }),
  successUrl: z.string().url("Success URL must be a valid URL"),
  cancelUrl: z.string().url("Cancel URL must be a valid URL"),
  couponId: z.string().optional()
});

// This function is used to create a Stripe Checkout Session (one-time payment or subscription)
// It's called by the <ButtonCheckout /> component
// By default, it doesn't force users to be authenticated. But if they are, it will prefill the Checkout data with their email and/or credit card
export async function POST(req) {
  try {
    const body = await req.json();

    // SECURITY: Validate input with Zod
    const parseResult = CheckoutSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json(
        { error: "Invalid input", details: parseResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = parseResult.data;

    const session = await getServerSession(authOptions);

    let user = null;
    if (session?.user?.id) {
      // Check cache first
      const userCacheKey = CacheKeys.user(session.user.id);
      user = await cache.get(userCacheKey);

      if (!user) {
        user = await prisma.user.findUnique({
          where: { id: session.user.id }
        });

        if (user) {
          // Cache user for 1 hour
          await cache.set(userCacheKey, user, 3600);
        }
      }
    }

    const { priceId, mode, successUrl, cancelUrl, couponId } = validatedData;

    const stripeSessionURL = await createCheckout({
      priceId,
      mode,
      successUrl,
      cancelUrl,
      // If user is logged in, it will pass the user ID to the Stripe Session so it can be retrieved in the webhook later
      clientReferenceId: user?.id?.toString(),
      // If user is logged in, this will automatically prefill Checkout data like email and/or credit card for faster checkout
      user,
      // If you send coupons from the frontend, you can pass it here
      couponId,
    });

    return NextResponse.json({ url: stripeSessionURL });
  } catch (e) {
    console.error('Stripe checkout error:', e.message);
    return NextResponse.json({ error: "Failed to create checkout session" }, { status: 500 });
  }
}
