import NextAuth from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { NextResponse } from "next/server";

const handler = NextAuth(authOptions);

// Add CORS headers for NextAuth endpoints
function addCorsHeaders(response) {
  const allowedOrigin = process.env.NODE_ENV === 'production'
    ? 'https://data.stalkapi.com'
    : '*';

  response.headers.set('Access-Control-Allow-Origin', allowedOrigin);
  response.headers.set('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Allow-Credentials', 'true');

  return response;
}

// Wrap handlers with CORS
async function GET(req) {
  const response = await handler(req);
  return addCorsHeaders(response);
}

async function POST(req) {
  const response = await handler(req);
  return addCorsHeaders(response);
}

async function OPTIONS(req) {
  const response = new NextResponse(null, { status: 200 });
  return addCorsHeaders(response);
}

export { GET, POST, OPTIONS };
