import { NextResponse } from 'next/server';

// SECURITY: Enforce admin API key in production
if (process.env.NODE_ENV === 'production' && !process.env.ADMIN_API_KEY) {
  throw new Error('❌ SECURITY ERROR: ADMIN_API_KEY must be set in production environment.');
}

// SECURITY: Validate admin API key strength
if (process.env.ADMIN_API_KEY) {
  const key = process.env.ADMIN_API_KEY;
  if (key.length < 32 || /demo|admin|test|changeme|default|1234|password/i.test(key)) {
    throw new Error('❌ SECURITY ERROR: ADMIN_API_KEY must be at least 32 characters and not contain weak/default values.');
  }
}

// Mock admin API key store (replace with DB or secure store in production)
const adminApiKeys = [
  {
    key: process.env.ADMIN_API_KEY || (process.env.NODE_ENV === 'development' ? 'dev_admin_key_for_testing_only_32chars' : null),
    allowedIps: ['127.0.0.1', '::1'], // Add production IPs here
    expiresAt: null, // Set to a Date string for expiration, or null for no expiry
  },
].filter(key => key.key !== null); // Remove null keys

export function middleware(req) {
  const apiKey = req.headers.get('x-admin-api-key');
  const clientIp = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || req.ip || req.connection?.remoteAddress || 'unknown';
  const now = new Date();

  // SECURITY: Ensure we have admin keys configured
  if (adminApiKeys.length === 0) {
    console.error('❌ SECURITY ERROR: No admin API keys configured');
    return new NextResponse(JSON.stringify({ error: 'Admin access temporarily unavailable.' }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const keyRecord = adminApiKeys.find(k => k.key === apiKey);

  if (!keyRecord) {
    console.warn('❌ Admin API access attempt with invalid key from IP:', clientIp);
    return new NextResponse(JSON.stringify({ error: 'Invalid or missing admin API key.' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // Optional: IP allowlisting
  if (keyRecord.allowedIps && keyRecord.allowedIps.length > 0 && !keyRecord.allowedIps.includes(clientIp)) {
    console.warn('❌ Admin API access attempt from unauthorized IP:', clientIp);
    return new NextResponse(JSON.stringify({ error: 'IP address not allowed for this admin API key.' }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // Optional: Key expiration
  if (keyRecord.expiresAt && now > new Date(keyRecord.expiresAt)) {
    console.warn('❌ Admin API access attempt with expired key from IP:', clientIp);
    return new NextResponse(JSON.stringify({ error: 'Admin API key expired.' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // Log successful admin access for security monitoring
  console.log('✅ Admin API access granted to IP:', clientIp);

  // Allow request to proceed
  return NextResponse.next();
}

export const config = {
  matcher: ['/api/admin/:path*'],
}; 