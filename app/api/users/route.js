import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
// import { prisma } from "@/libs/prisma";  // Commented out since endpoint is disabled
// import { cache } from "@/libs/redis";   // Commented out since endpoint is disabled

// SECURITY: Admin-only endpoint to get all users
export async function GET() {
  try {
    // SECURITY: Require authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // SECURITY: Check if user is admin (you should implement proper admin role checking)
    // For now, we'll restrict this endpoint completely as it's too sensitive
    return NextResponse.json(
      { error: 'This endpoint has been disabled for security reasons. Use admin API instead.' },
      { status: 403 }
    );

    // TODO: Implement proper admin role checking before re-enabling
    /*
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { isAdmin: true } // Add isAdmin field to your user model
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check cache first
    const cacheKey = 'all_users';
    let users = await cache.get(cacheKey);

    if (!users) {
      users = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          hasAccess: true,
          customerId: true,
          priceId: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Cache for 5 minutes
      await cache.set(cacheKey, users, 300);
    }

    return NextResponse.json({
      success: true,
      count: users.length,
      users: users
    });
    */
  } catch (error) {
    console.error('Users API error:', error.message);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
