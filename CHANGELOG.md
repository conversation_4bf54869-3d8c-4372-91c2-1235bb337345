# Changelog

All notable changes to the StalkAPI Next.js Frontend project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Fixed - 2024-12-19
- **UsageChart Component**: Fixed chart rendering issues that prevented data visualization
  - Resolved nested div structure problems causing visual conflicts
  - Fixed height calculation logic for chart bars
  - Added proper data type conversion and validation
  - Improved layout and CSS positioning
  - Enhanced error handling for edge cases
  - **24h Chart Layout**: Fixed horizontal scrollbar issue by reducing data points
    - Changed from 24 hourly intervals to 8 three-hourly intervals (00:00, 03:00, 06:00, etc.)
    - Fixed time format to show clean hours (08:00) instead of minutes (08:08)
    - Improved responsive design for better mobile experience
    - Removed horizontal overflow with better spacing and sizing

- **Dashboard Chart Data Generation**: Completely overhauled time range logic for accurate data representation
  - **24h chart**: Now shows rolling 24-hour window from current time backwards with hourly intervals
  - **7d chart**: Now shows rolling 7-day window from current date backwards with daily intervals (MM/DD format)
  - **30d chart**: Now shows rolling 4-week window from current date backwards with weekly intervals (MM/DD format)
  - **Data ordering**: Chronological order with most recent data points on the right side
  - **SQL queries**: Updated to use proper DATE_TRUNC functions for accurate time bucketing
  - **Time labels**: Now show actual dates/times instead of generic labels

### Technical Details
- **Files Modified**:
  - `components/dashboard/UsageChart.js`: Chart rendering fixes and data validation
  - `app/dashboard/page.js`: Complete rewrite of chart data generation logic

- **Database Query Improvements**:
  - Added proper time bucketing with DATE_TRUNC for accurate grouping
  - Implemented rolling time windows instead of fixed calendar periods
  - Enhanced data merging logic to handle missing time slots

- **Chart Visualization Enhancements**:
  - Fixed bar height calculations using pixel-based measurements
  - Added minimum height constraints for visibility
  - Improved tooltip positioning and content
  - Enhanced responsive layout for different screen sizes

### Added
- Project re-indexing and comprehensive documentation
- Changelog tracking for all future changes
- Comprehensive documentation structure in `/docs` folder
- Debug logging for chart data flow troubleshooting
- **ApiKeyManager Component**: Implemented real API key display functionality
  - Shows actual API key from database instead of mock data
  - Dynamic API key masking with show/hide toggle
  - Copy to clipboard functionality with success feedback
  - API key statistics display (created date, last used, usage count)
  - Secure API key handling with proper masking algorithm

- **WebSocketStatus Component**: Implemented real WebSocket session monitoring
  - Shows actual WebSocket sessions from database instead of mock data
  - Proper WebSocket connection status: connected (disconnected_at is null) or disconnected
  - Session details including subscribed streams, IP address, and activity timestamps
  - Connection limit tracking with visual indicators
  - Empty state handling for when no connections are active
  - Simple status logic based on disconnected_at field from database

- **RecentActivity Component**: Implemented real API usage activity tracking
  - Shows actual API usage data from api_usage_logs table instead of mock data
  - Displays recent API calls with endpoint, method, and credits consumed
  - Real-time activity filtering by type (API calls, WebSocket, Authentication)
  - Activity details including IP address and user agent information
  - Smart activity type detection (WebSocket streams, API calls, auth events)
  - Empty state handling for users with no API activity
  - Proper timestamp formatting with relative time display
  - Color-coded method badges (GET, POST, STREAM, etc.)

- **Bug Fixes**: Fixed database schema compatibility issues
  - Removed references to non-existent columns (request_id, response_time_ms, status_code)
  - Updated queries to match actual api_usage_logs table structure
  - Fixed all compilation and runtime errors
  - Cleaned up debug logging for production readiness

## [1.0.0] - 2024-12-19

### Added
- Initial Next.js frontend application for StalkAPI
- PostgreSQL database integration with Prisma ORM
- Redis caching for performance optimization
- NextAuth.js authentication system with Google OAuth and email magic links
- Stripe payment integration with multiple pricing tiers
- Modern responsive UI with DaisyUI and Tailwind CSS
- Dark/light theme support
- API client for backend communication
- Dashboard for user account management
- Security enhancements and bank-level security standards
- Rate limiting and input validation
- Email functionality with nodemailer
- SEO optimization with next-sitemap
- Comprehensive error handling and logging

### Security
- Server-side only database access (Prisma & Redis clients)
- SQL injection protection via Prisma's type-safe queries
- Rate limiting on API endpoints
- Input validation and sanitization
- Secure connection pooling
- Authentication system with configurable providers

### Changed
- Migrated from MongoDB to PostgreSQL for better performance and security
- Replaced Mongoose with Prisma ORM
- Updated authentication system to use Prisma adapter
- Enhanced security measures throughout the application

### Removed
- MongoDB and Mongoose dependencies
- Legacy database models and plugins

## Migration History

### Database Migration (MongoDB → PostgreSQL)
- **Date**: 2024-12-19
- **Reason**: Better performance, security, and ACID compliance
- **Impact**: Complete database layer rewrite
- **Files Affected**: All API routes, authentication system, database models
- **New Dependencies**: @prisma/client, prisma, pg, ioredis
- **Removed Dependencies**: mongoose, mongodb

### Authentication Enhancement
- **Date**: 2024-12-19
- **Changes**: Added configurable authentication providers
- **New Features**: Toggle-able Google OAuth and email authentication
- **Configuration**: Added `enabledAuth` object in config.js

### UI/UX Improvements
- **Date**: 2024-12-19
- **Changes**: Modern design with DaisyUI components
- **Features**: Dark/light theme toggle, responsive design
- **Components**: Comprehensive component library for reusability

---

## How to Use This Changelog

### For Developers
- Check this file before starting work to understand recent changes
- Add entries for all significant changes you make
- Follow the format: Added/Changed/Deprecated/Removed/Fixed/Security

### For Deployment
- Review changelog before each deployment
- Ensure all database migrations are documented
- Check for breaking changes that might affect production

### Entry Format
```markdown
### Added/Changed/Fixed/etc.
- Brief description of the change
- Impact on users or system
- Related files or components affected
```

---

**Note**: This changelog was created during the project re-indexing process. All future changes should be documented here as they are made.
